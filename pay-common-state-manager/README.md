# Pay Common State Manager 接入文档

## 📖 概述

`pay-common-state-manager` 是一个通用的状态管理组件，提供了灵活的业务状态管理功能，支持多种业务类型的状态控制和子状态管理。

## 🚀 快速开始

### 1. 添加依赖

在您的 Spring Boot 项目的 `pom.xml` 中添加以下依赖：

```xml
<dependency>
    <groupId>com.wosai.pay</groupId>
    <artifactId>pay-common-state-manager</artifactId>
    <version>1.2.7-SNAPSHOT</version>
</dependency>
```

### 2. 配置数据源

确保您的项目中已配置数据源，状态管理器需要数据库支持：

```yaml
spring:
  datasource:
    url: *****************************************
    username: your_username
    password: your_password
    driver-class-name: com.mysql.cj.jdbc.Driver
```

### 3. 启用组件扫描

在您的主配置类上添加组件扫描：

```java
@SpringBootApplication
@ComponentScan(basePackages = {
    "com.your.package",
    "com.wosai.pay.common.state.manager"
})
public class YourApplication {
    public static void main(String[] args) {
        SpringApplication.run(YourApplication.class, args);
    }
}
```

## 🏗️ 核心概念

### 业务状态模型

- **业务类型 (businessType)**: 区分不同的业务场景
- **子状态类型 (subStateType)**: 具体的状态项
- **状态值**: 每个子状态的开启/关闭状态
- **总状态**: 所有子状态的综合状态

### 核心组件

1. **StateManagerService**: 状态管理服务接口
2. **StateManagerProcessor**: 业务处理器接口
3. **StateManagerConfig**: 配置管理器
4. **DTO类**: 数据传输对象

## 📋 数据库表结构

### 1. 通用状态表 (common_state)

```sql
CREATE TABLE `common_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `business_type` varchar(50) NOT NULL COMMENT '业务类型',
  `entity_id` varchar(100) NOT NULL COMMENT '实体ID',
  `state` tinyint(1) DEFAULT '1' COMMENT '总状态',
  `sub_states_bits` varchar(500) DEFAULT NULL COMMENT '子状态位串',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_business_entity` (`business_type`, `entity_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通用状态表';
```

### 2. 业务禁用原因表 (business_disable_reason)

```sql
CREATE TABLE `business_disable_reason` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `business_type` varchar(50) NOT NULL COMMENT '业务类型',
  `sub_state_type` int(11) NOT NULL COMMENT '子状态类型',
  `description` varchar(200) NOT NULL COMMENT '状态描述',
  `version` int(11) DEFAULT '0' COMMENT '版本号',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_business_substate` (`business_type`, `sub_state_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='业务禁用原因表';
```

## 🔧 使用指南

### 1. 实现业务处理器

创建您的业务处理器，实现 `StateManagerProcessor` 接口：

```java
@Component
public class YourBusinessProcessor implements StateManagerProcessor<YourStateDTO, YourStateResult> {
    
    @Override
    public void generateCriteria(Criteria criteria, YourStateDTO request) {
        // 添加业务特定的查询条件
        criteria.and("entityId").is(request.getEntityId());
    }
    
    @Override
    public CommonStateEntity buildCommonStateEntity(YourStateDTO request) {
        // 构建状态实体
        CommonStateEntity entity = new CommonStateEntity();
        entity.setEntityId(request.getEntityId());
        return entity;
    }
    
    @Override
    public YourStateResult createResultInstance(YourStateDTO request) {
        // 创建结果实例
        return new YourStateResult();
    }
    
    @Override
    public void processStateChange(YourStateDTO previousState, YourStateDTO currentState, 
                                 OperationLogDTO operationLogRequest) {
        // 处理状态变更逻辑
        // 可以在这里添加业务特定的处理逻辑
    }
}
```

### 2. 注册处理器

在处理器注册表中注册您的处理器：

```java
@PostConstruct
public void registerProcessor() {
    StateManagerProcessorRegistry.registerProcessor("YOUR_BUSINESS_TYPE", yourBusinessProcessor);
}
```

### 3. 创建 DTO 类

#### 状态查询/更新 DTO

```java
public class YourStateDTO extends StateManagerBaseDTO {
    private String entityId;

    // 构造函数
    public YourStateDTO(String processorType, String businessType,
                       Integer subStateType, Boolean enabled, String entityId) {
        super(processorType, businessType, subStateType, enabled);
        this.entityId = entityId;
    }

    // getter/setter
    public String getEntityId() { return entityId; }
    public void setEntityId(String entityId) { this.entityId = entityId; }
}
```

#### 状态结果 DTO

```java
public class YourStateResult extends StateManagerResult {
    private String entityId;

    // getter/setter
    public String getEntityId() { return entityId; }
    public void setEntityId(String entityId) { this.entityId = entityId; }
}
```

### 4. 使用状态管理服务

在您的业务代码中注入并使用状态管理服务：

```java
@Service
public class YourBusinessService {

    @Autowired
    private StateManagerService stateManagerService;

    /**
     * 查询状态
     */
    public YourStateResult queryState(String entityId) {
        YourStateDTO queryRequest = new YourStateDTO(
            "YOUR_PROCESSOR_TYPE",
            "YOUR_BUSINESS_TYPE",
            null,
            null,
            entityId
        );

        return stateManagerService.queryState(queryRequest);
    }

    /**
     * 更新状态
     */
    public Boolean updateState(String entityId, Integer subStateType, Boolean enabled,
                              String operatorUserId, String operatorUserName) {
        // 构建状态更新请求
        YourStateDTO updateRequest = new YourStateDTO(
            "YOUR_PROCESSOR_TYPE",
            "YOUR_BUSINESS_TYPE",
            subStateType,
            enabled,
            entityId
        );
        updateRequest.setRemark("状态更新");

        // 构建操作日志
        OperationLogDTO operationLog = new OperationLogDTO(
            operatorUserId,
            operatorUserName,
            "UPDATE_STATE_" + System.currentTimeMillis()
        );

        return stateManagerService.updateState(updateRequest, operationLog);
    }
}
```

## 📊 API 接口示例

### 1. 状态查询接口

```java
@RestController
@RequestMapping("/api/state")
public class StateController {

    @Autowired
    private YourBusinessService businessService;

    @GetMapping("/query/{entityId}")
    public ResponseEntity<YourStateResult> queryState(@PathVariable String entityId) {
        try {
            YourStateResult result = businessService.queryState(entityId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PostMapping("/update")
    public ResponseEntity<Boolean> updateState(@RequestBody StateUpdateRequest request) {
        try {
            Boolean result = businessService.updateState(
                request.getEntityId(),
                request.getSubStateType(),
                request.getEnabled(),
                request.getOperatorUserId(),
                request.getOperatorUserName()
            );
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
```

### 2. 请求/响应示例

#### 查询状态响应

```json
{
  "entityId": "entity_123",
  "state": true,
  "subStates": [
    {
      "type": 1,
      "desc": "支付功能",
      "value": true
    },
    {
      "type": 2,
      "desc": "退款功能",
      "value": false
    }
  ]
}
```

#### 更新状态请求

```json
{
  "entityId": "entity_123",
  "subStateType": 2,
  "enabled": true,
  "operatorUserId": "user_456",
  "operatorUserName": "张三"
}
```

## 🌐 JSON-RPC 接口

### 1. 启用 JSON-RPC 服务

在您的 Spring Boot 应用中添加配置类：

```java
@Configuration
@Import(StateManagerJsonRpcConfig.class)
public class YourApplicationConfig {
    // 其他配置
}
```

或者在 `application.yml` 中配置：

```yaml
state:
  manager:
    jsonrpc:
      enabled: true
      url-mapping: /jsonrpc/*
      content-type: application/json
```

### 2. JSON-RPC 接口说明

状态管理提供了统一的 JSON-RPC 接口，支持以下方法：

- `queryState`: 查询状态
- `changeState`: 修改状态
- `batchQueryState`: 批量查询状态
- `batchChangeState`: 批量修改状态

### 3. JSON-RPC 调用示例

#### 查询状态

**请求：**
```json
{
  "jsonrpc": "2.0",
  "method": "queryState",
  "params": {
    "entityType": "ProviderMchIdEntity",
    "business": "settlement",
    "entity": {
      "provider": 1001,
      "providerMchId": "test_mch_001"
    },
    "traceId": "trace-001"
  },
  "id": 1
}
```

**响应：**
```json
{
  "jsonrpc": "2.0",
  "result": {
    "code": 200,
    "message": "Success",
    "data": {
      "provider": 1001,
      "providerMchId": "test_mch_001",
      "state": true,
      "subStateList": [
        {
          "type": 1,
          "desc": "支付功能",
          "value": true
        },
        {
          "type": 2,
          "desc": "退款功能",
          "value": false
        }
      ]
    },
    "traceId": "trace-001",
    "timestamp": 1640995200000
  },
  "id": 1
}
```

#### 修改状态

**请求：**
```json
{
  "jsonrpc": "2.0",
  "method": "changeState",
  "params": {
    "entityType": "ProviderMchIdEntity",
    "business": "settlement",
    "subStateType": 1,
    "enabled": true,
    "entity": {
      "provider": 1001,
      "providerMchId": "test_mch_001"
    },
    "remark": "启用支付功能",
    "operationLog": {
      "operatorUserId": "user_001",
      "operatorUserName": "管理员",
      "operationId": "op_001",
      "description": "手动启用支付功能"
    },
    "traceId": "trace-002"
  },
  "id": 2
}
```

**响应：**
```json
{
  "jsonrpc": "2.0",
  "result": {
    "code": 200,
    "message": "Success",
    "data": true,
    "traceId": "trace-002",
    "timestamp": 1640995200000
  },
  "id": 2
}
```

#### 批量查询状态

**请求：**
```json
{
  "jsonrpc": "2.0",
  "method": "batchQueryState",
  "params": [
    {
      "entityType": "ProviderMchIdEntity",
      "business": "settlement",
      "entity": {
        "provider": 1001,
        "providerMchId": "test_mch_001"
      }
    },
    {
      "entityType": "ProviderMchIdEntity",
      "business": "settlement",
      "entity": {
        "provider": 1002,
        "providerMchId": "test_mch_002"
      }
    }
  ],
  "id": 3
}
```

### 4. 客户端调用示例

#### Java 客户端

```java
@Service
public class StateManagerJsonRpcClient {

    @Autowired
    private JsonRpcHttpClient jsonRpcClient;

    public StateQueryResult queryState(String entityType, String business, Map<String, Object> entity) {
        StateQueryRequest request = new StateQueryRequest(entityType, business, entity);

        try {
            JsonRpcResponse<StateQueryResult> response = jsonRpcClient.invoke(
                "queryState",
                request,
                new TypeReference<JsonRpcResponse<StateQueryResult>>() {}
            );

            if (response.getCode() == 200) {
                return response.getData();
            } else {
                throw new RuntimeException("Query failed: " + response.getMessage());
            }
        } catch (Exception e) {
            throw new RuntimeException("JSON-RPC call failed", e);
        }
    }

    public Boolean changeState(String entityType, String business, Integer subStateType,
                              Boolean enabled, Map<String, Object> entity, String remark) {
        StateUpdateRequest request = new StateUpdateRequest(entityType, business, subStateType, enabled, entity);
        request.setRemark(remark);

        try {
            JsonRpcResponse<Boolean> response = jsonRpcClient.invoke(
                "changeState",
                request,
                new TypeReference<JsonRpcResponse<Boolean>>() {}
            );

            if (response.getCode() == 200) {
                return response.getData();
            } else {
                throw new RuntimeException("Change state failed: " + response.getMessage());
            }
        } catch (Exception e) {
            throw new RuntimeException("JSON-RPC call failed", e);
        }
    }
}
```

#### HTTP 客户端

```bash
# 查询状态
curl -X POST http://localhost:8080/jsonrpc/stateManager \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "method": "queryState",
    "params": {
      "entityType": "ProviderMchIdEntity",
      "business": "settlement",
      "entity": {
        "provider": 1001,
        "providerMchId": "test_mch_001"
      }
    },
    "id": 1
  }'

# 修改状态
curl -X POST http://localhost:8080/jsonrpc/stateManager \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "method": "changeState",
    "params": {
      "entityType": "ProviderMchIdEntity",
      "business": "settlement",
      "subStateType": 1,
      "enabled": true,
      "entity": {
        "provider": 1001,
        "providerMchId": "test_mch_001"
      },
      "remark": "启用支付功能"
    },
    "id": 2
  }'
```

## ⚙️ 配置管理

### 1. 初始化业务配置数据

在 `business_disable_reason` 表中插入您的业务配置：

```sql
INSERT INTO business_disable_reason (business_type, sub_state_type, description) VALUES
('YOUR_BUSINESS_TYPE', 1, '支付功能'),
('YOUR_BUSINESS_TYPE', 2, '退款功能'),
('YOUR_BUSINESS_TYPE', 3, '查询功能');
```

### 2. 配置刷新

配置支持定时刷新，默认每5分钟刷新一次。您也可以手动触发刷新：

```java
@Autowired
private StateManagerConfig stateManagerConfig;

// 手动刷新配置
stateManagerConfig.load();
```

## 🔍 监控和日志

### 1. 日志配置

在 `logback-spring.xml` 中添加状态管理器的日志配置：

```xml
<logger name="com.wosai.pay.common.state.manager" level="INFO" additivity="false">
    <appender-ref ref="CONSOLE"/>
    <appender-ref ref="FILE"/>
</logger>
```

### 2. 监控指标

组件提供了以下监控点：
- 状态查询次数
- 状态更新次数
- 配置刷新次数
- 异常统计


## 🆘 常见问题

### Q1: 如何添加新的业务类型？

A: 在 `business_disable_reason` 表中插入新的业务类型配置，然后实现对应的处理器即可。

### Q2: 状态位串是如何工作的？

A: 状态位串是一个字符串，每个位置代表一个子状态的开启/关闭状态，'1' 表示开启，'0' 表示关闭。

### Q3: 如何自定义状态变更逻辑？

A: 在您的处理器的 `processStateChange` 方法中实现自定义逻辑。
