package com.wosai.pay.common.state.manager.jsonrpc;

import com.wosai.pay.common.state.manager.jsonrpc.dto.StateQueryRequest;
import com.wosai.pay.common.state.manager.jsonrpc.dto.StateUpdateRequest;
import com.wosai.pay.common.state.manager.request.AbstractEntity;
import com.wosai.pay.common.state.manager.request.OperationLogRequest;
import com.wosai.pay.common.state.manager.request.StateManagerRequest;
import com.wosai.pay.common.state.manager.result.StateManagerResult;
import com.wosai.pay.common.state.manager.service.StateManagerService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.ValidationException;
import javax.validation.Validator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 状态管理 JSON-RPC 服务实现
 *
 * <AUTHOR>
 * @since 1.2.7-SNAPSHOT
 */
public class StateManagerJsonRpcServiceImpl implements StateManagerJsonRpcService {

    private static final Logger logger = LoggerFactory.getLogger(StateManagerJsonRpcServiceImpl.class);

    @Resource
    private StateManagerService stateManagerService;

    @Resource
    private Validator validator; // JSR-380 Validator

    @Override
    public StateManagerResult queryState(StateQueryRequest request) {
        try {
            logger.info("JSON-RPC queryState request received,  business: {}", request.getBusiness());
            validate(request);
            // 参数验证
            if (StringUtils.isBlank(request.getBusiness()) || request.getEntity() == null) {
                throw new IllegalArgumentException("Missing required parameters:  business, or entity");
            }
            // 转换为内部请求对象
            StateManagerRequest<AbstractEntity> stateManagerRequest = convertToStateManagerRequest(request);
            // 调用状态管理服务
            StateManagerResult result = stateManagerService.queryState(stateManagerRequest);
            logger.info("JSON-RPC queryState completed successfully, result: {}", result);
            return result;
        } catch (IllegalArgumentException illegalArgumentException) {
            throw illegalArgumentException;
        } catch (Exception e) {
            logger.error("JSON-RPC queryState internal error, ", e);
            throw e;
        }
    }

    @Override
    public Boolean changeState(StateUpdateRequest request, OperationLogRequest operationLogRequest) {
        try {
            logger.info("JSON-RPC changeState request received business: {}, subStateType: {}, enabled: {}", request.getBusiness(), request.getSubStateType(), request.getEnabled());
            // 参数验证
            if (StringUtils.isBlank(request.getBusiness()) || request.getEntity() == null || request.getSubStateType() == null || request.getEnabled() == null) {
                throw new IllegalArgumentException("Missing required parameters:  business, or entity, subStateType, or enabled");
            }
            // 转换为内部请求对象
            StateManagerRequest<AbstractEntity> stateManagerRequest = convertToStateManagerRequest(request);
            // 调用状态管理服务
            Boolean result = stateManagerService.changeState(stateManagerRequest, operationLogRequest);
            logger.info("JSON-RPC changeState completed successfully result: {}", result);
            return result;
        } catch (IllegalArgumentException illegalArgumentException) {
            throw illegalArgumentException;
        } catch (Exception e) {
            logger.error("JSON-RPC changeState internal error", e);
            throw e;
        }
    }

    /**
     * 转换查询请求为内部请求对象
     */
    private StateManagerRequest<AbstractEntity> convertToStateManagerRequest(StateQueryRequest request) {
        StateManagerRequest<AbstractEntity> stateManagerRequest = new StateManagerRequest<>();
        stateManagerRequest.setBusiness(request.getBusiness());
        // 转换实体对象
        stateManagerRequest.setEntity(request.getEntity());
        return stateManagerRequest;
    }

    /**
     * 转换修改请求为内部请求对象
     */
    private StateManagerRequest<AbstractEntity> convertToStateManagerRequest(StateUpdateRequest request) {
        StateManagerRequest<AbstractEntity> stateManagerRequest = new StateManagerRequest<>();
        stateManagerRequest.setBusiness(request.getBusiness());
        stateManagerRequest.setSubStateType(request.getSubStateType());
        stateManagerRequest.setEnabled(request.getEnabled());
        stateManagerRequest.setRemark(request.getRemark());
        // 转换实体对象
        stateManagerRequest.setEntity(request.getEntity());
        return stateManagerRequest;
    }

    private <T> void validate(T object) {
        Set<ConstraintViolation<T>> violations = validator.validate(object);
        if (!violations.isEmpty()) {
            List<String> errors = violations.stream().map(ConstraintViolation::getMessage).collect(Collectors.toList());
            throw new ValidationException("Validation failed" + errors);
        }
    }


}
