package com.wosai.pay.common.state.manager.service;

import com.wosai.pay.common.state.manager.request.AbstractEntity;
import com.wosai.pay.common.state.manager.request.StateManagerRequest;
import com.wosai.pay.common.state.manager.request.OperationLogRequest;
import com.wosai.pay.common.state.manager.result.StateManagerResult;
import com.wosai.pay.common.state.manager.registry.StateManagerProcessor;

public interface StateManagerService {

    boolean registerProcessor(StateManagerProcessor<?,?, ?> processor);

    <T extends AbstractEntity, R extends StateManagerResult> R queryState(StateManagerRequest<T> stateManagerRequest);

   default  <T extends AbstractEntity, R extends StateManagerResult> Boolean changeState(StateManagerRequest<T> stateManagerRequest){
       return changeState(stateManagerRequest, null);
   }

    <T extends AbstractEntity, R extends StateManagerResult> Boolean changeState(StateManagerRequest<T> stateManagerRequest, OperationLogRequest operationLogRequest);

}
