package com.wosai.pay.common.state.manager.autoconfigure;

import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * State Manager 配置属性
 * 
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "pay.state.manager")
public class StateManagerProperties {

    /**
     * 是否启用状态管理器
     */
    private boolean enabled = true;

    /**
     * 数据库配置
     */
    private Database database = new Database();

    /**
     * Kafka 配置
     */
    private Kafka kafka = new Kafka();

    /**
     * 业务日志服务配置
     */
    private BusinessLog businessLog = new BusinessLog();

    /**
     * 状态配置管理
     */
    private Config config = new Config();

    /**
     * JSON-RPC 配置
     */
    private JsonRpc jsonRpc = new JsonRpc();

    public static class Database {
        /**
         * 状态表名
         */
        private String stateTableName = "state";

        /**
         * 业务配置表名
         */
        private String configTableName = "business_disable_reason";

        public String getStateTableName() {
            return stateTableName;
        }

        public void setStateTableName(String stateTableName) {
            this.stateTableName = stateTableName;
        }

        public String getConfigTableName() {
            return configTableName;
        }

        public void setConfigTableName(String configTableName) {
            this.configTableName = configTableName;
        }
    }

    public static class Kafka {
        /**
         * 是否启用 Kafka
         */
        private boolean enabled = true;

        /**
         * Kafka 服务器地址
         */
        private String bootstrapServers = "localhost:9092";

        /**
         * ACK 配置
         */
        private String acks = "1";

        /**
         * 批处理大小
         */
        private int batchSize = 16384;

        /**
         * 延迟时间
         */
        private int lingerMs = 1000;

        /**
         * 请求超时时间
         */
        private int requestTimeoutMs = 30000;

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public String getBootstrapServers() {
            return bootstrapServers;
        }

        public void setBootstrapServers(String bootstrapServers) {
            this.bootstrapServers = bootstrapServers;
        }

        public String getAcks() {
            return acks;
        }

        public void setAcks(String acks) {
            this.acks = acks;
        }

        public int getBatchSize() {
            return batchSize;
        }

        public void setBatchSize(int batchSize) {
            this.batchSize = batchSize;
        }

        public int getLingerMs() {
            return lingerMs;
        }

        public void setLingerMs(int lingerMs) {
            this.lingerMs = lingerMs;
        }

        public int getRequestTimeoutMs() {
            return requestTimeoutMs;
        }

        public void setRequestTimeoutMs(int requestTimeoutMs) {
            this.requestTimeoutMs = requestTimeoutMs;
        }
    }

    public static class BusinessLog {
        /**
         * 是否启用业务日志
         */
        private boolean enabled = true;

        /**
         * 业务日志服务 URL
         */
        private String serviceUrl = "http://business-logstash.beta.iwosai.com/rpc/businessOpLog";

        /**
         * 服务名称
         */
        private String serverName = "business-logstash";

        /**
         * 连接超时时间（毫秒）
         */
        private int connectionTimeoutMillis = 1000;

        /**
         * 读取超时时间（毫秒）
         */
        private int readTimeoutMillis = 3000;

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public String getServiceUrl() {
            return serviceUrl;
        }

        public void setServiceUrl(String serviceUrl) {
            this.serviceUrl = serviceUrl;
        }

        public String getServerName() {
            return serverName;
        }

        public void setServerName(String serverName) {
            this.serverName = serverName;
        }

        public int getConnectionTimeoutMillis() {
            return connectionTimeoutMillis;
        }

        public void setConnectionTimeoutMillis(int connectionTimeoutMillis) {
            this.connectionTimeoutMillis = connectionTimeoutMillis;
        }

        public int getReadTimeoutMillis() {
            return readTimeoutMillis;
        }

        public void setReadTimeoutMillis(int readTimeoutMillis) {
            this.readTimeoutMillis = readTimeoutMillis;
        }
    }

    public static class Config {
        /**
         * 是否启用自动刷新
         */
        private boolean enableAutoRefresh = true;

        /**
         * 刷新间隔（秒）
         */
        private long refreshIntervalSeconds = 300;

        public boolean isEnableAutoRefresh() {
            return enableAutoRefresh;
        }

        public void setEnableAutoRefresh(boolean enableAutoRefresh) {
            this.enableAutoRefresh = enableAutoRefresh;
        }

        public long getRefreshIntervalSeconds() {
            return refreshIntervalSeconds;
        }

        public void setRefreshIntervalSeconds(long refreshIntervalSeconds) {
            this.refreshIntervalSeconds = refreshIntervalSeconds;
        }
    }

    public static class JsonRpc {
        /**
         * 是否启用 JSON-RPC
         */
        private boolean enabled = true;

        /**
         * JSON-RPC 服务路径
         */
        private String servicePath = "/12";

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public String getServicePath() {
            return servicePath;
        }

        public void setServicePath(String servicePath) {
            this.servicePath = servicePath;
        }
    }

    // Getters and Setters
    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public Database getDatabase() {
        return database;
    }

    public void setDatabase(Database database) {
        this.database = database;
    }

    public Kafka getKafka() {
        return kafka;
    }

    public void setKafka(Kafka kafka) {
        this.kafka = kafka;
    }

    public BusinessLog getBusinessLog() {
        return businessLog;
    }

    public void setBusinessLog(BusinessLog businessLog) {
        this.businessLog = businessLog;
    }

    public Config getConfig() {
        return config;
    }

    public void setConfig(Config config) {
        this.config = config;
    }

    public JsonRpc getJsonRpc() {
        return jsonRpc;
    }

    public void setJsonRpc(JsonRpc jsonRpc) {
        this.jsonRpc = jsonRpc;
    }
}
