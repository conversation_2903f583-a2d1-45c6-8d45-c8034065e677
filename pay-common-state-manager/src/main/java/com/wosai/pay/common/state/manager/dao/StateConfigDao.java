package com.wosai.pay.common.state.manager.dao;


import com.wosai.pay.common.data.Jackson2PersistenceHelper;
import com.wosai.pay.common.data.jdbc.JdbcVersionedRecordDao;
import com.wosai.pay.common.state.manager.constant.StateManagerConstant;
import com.wosai.pay.common.state.manager.domain.BizDisableReasonDO;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

public class StateConfigDao extends JdbcVersionedRecordDao<Long, BizDisableReasonDO> {

    private static final String TABLE_NAME = StateManagerConstant.TABLE_BUSINESS_CONFIG;

    public StateConfigDao(NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        super(TABLE_NAME, BizDisableReasonDO.class, "", namedParameterJdbcTemplate, new Jackson2PersistenceHelper());
    }
}

