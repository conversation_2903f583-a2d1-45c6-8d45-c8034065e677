package com.wosai.pay.common.state.manager.jsonrpc.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.pay.common.state.manager.request.AbstractEntity;
import com.wosai.pay.common.state.manager.request.OperationLogRequest;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 状态修改请求 DTO
 *
 * <AUTHOR>
 * @since 1.2.7-SNAPSHOT
 */
@Data
public class StateUpdateRequest {

    /**
     * 业务类型
     * 例如: "settlement", "payment"
     */
    @JsonProperty("business")
    @NotBlank(message = "business不能为空")
    private String business;

    /**
     * 子状态类型
     * 指定要修改的子状态类型
     */
    @JsonProperty("subStateType")
    @NotNull(message = "subStateType不能为空")
    private Integer subStateType;

    /**
     * 状态值
     * true: 启用, false: 禁用
     */
    @JsonProperty("enabled")
    @NotNull(message = "enabled不能为空")
    private Boolean enabled;

    /**
     * 实体数据
     * 包含修改所需的实体信息，如 provider, providerMchId 等
     */
    @JsonProperty("entity")
    @NotNull(message = "entity不能为空")
    private AbstractEntity entity;

    /**
     * 备注信息
     */
    @JsonProperty("remark")
    private String remark;

    /**
     * 操作日志信息
     */
    @JsonProperty("operationLogRequest")
    private OperationLogRequest operationLogRequest;

}
