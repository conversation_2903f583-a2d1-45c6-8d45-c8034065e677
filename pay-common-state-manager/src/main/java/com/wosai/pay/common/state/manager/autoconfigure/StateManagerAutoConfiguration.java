package com.wosai.pay.common.state.manager.autoconfigure;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImplExporter;
import com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean;
import com.googlecode.jsonrpc4j.spring.JsonServiceExporter;
import com.wosai.pay.common.base.util.MapUtil;
import com.wosai.pay.common.state.manager.dao.StateConfigDao;
import com.wosai.pay.common.state.manager.dao.StateDao;
import com.wosai.pay.common.state.manager.jsonrpc.StateManagerJsonRpcService;
import com.wosai.pay.common.state.manager.jsonrpc.StateManagerJsonRpcServiceImpl;
import com.wosai.pay.common.state.manager.request.AbstractEntity;
import com.wosai.pay.common.state.manager.service.StateManagerConfig;
import com.wosai.pay.common.state.manager.service.StateManagerService;
import com.wosai.pay.common.state.manager.service.StateManagerServiceImpl;
import com.wosai.sp.business.logstash.service.BusinessOpLogService;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;

import java.util.Map;

/**
 * State Manager 自动配置类
 * 
 * <AUTHOR>
 */
@Configuration
@ConditionalOnProperty(prefix = "pay.state.manager", name = "enabled", havingValue = "true", matchIfMissing = true)
@EnableConfigurationProperties(StateManagerProperties.class)
@ComponentScan(basePackages = "com.wosai.pay.common.state.manager.jsonrpc")
public class StateManagerAutoConfiguration {

    private final StateManagerProperties properties;

    public StateManagerAutoConfiguration(StateManagerProperties properties) {
        this.properties = properties;
    }

    /**
     * 状态配置 DAO
     */
    @Bean
    @ConditionalOnMissingBean
    public StateConfigDao stateConfigDao(NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        return new StateConfigDao(namedParameterJdbcTemplate);
    }

    /**
     * 状态 DAO
     */
    @Bean
    @ConditionalOnMissingBean
    public StateDao stateDao(NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        return new StateDao(properties.getDatabase().getStateTableName(), namedParameterJdbcTemplate);
    }

    /**
     * 状态管理配置
     */
    @Bean
    @ConditionalOnMissingBean
    public StateManagerConfig stateManagerConfig(StateConfigDao stateConfigDao) {
        return new StateManagerConfig(
            stateConfigDao,
            properties.getConfig().getRefreshIntervalSeconds(),
            properties.getConfig().isEnableAutoRefresh()
        );
    }

    /**
     * 业务日志服务代理
     */
    @Bean
    @ConditionalOnProperty(prefix = "pay.state.manager.business-log", name = "enabled", havingValue = "true", matchIfMissing = true)
    @ConditionalOnClass(name = "com.wosai.sp.business.logstash.service.BusinessOpLogService")
    @ConditionalOnMissingBean
    public JsonProxyFactoryBean businessOpLogService() {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl(properties.getBusinessLog().getServiceUrl());
        jsonProxyFactoryBean.setServiceInterface(BusinessOpLogService.class);
        jsonProxyFactoryBean.setServerName(properties.getBusinessLog().getServerName());
        jsonProxyFactoryBean.setConnectionTimeoutMillis(properties.getBusinessLog().getConnectionTimeoutMillis());
        jsonProxyFactoryBean.setReadTimeoutMillis(properties.getBusinessLog().getReadTimeoutMillis());
        return jsonProxyFactoryBean;
    }

    /**
     * Kafka 模板
     */
    @Bean("stateManagerKafkaTemplate")
    @ConditionalOnProperty(prefix = "pay.state.manager.kafka", name = "enabled", havingValue = "true", matchIfMissing = true)
    @ConditionalOnClass(name = "org.springframework.kafka.core.KafkaTemplate")
    @ConditionalOnMissingBean(name = "stateManagerKafkaTemplate")
    public KafkaTemplate<String, Object> stateManagerKafkaTemplate() {
        Map<String, Object> configs = MapUtil.hashMap(
                ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, properties.getKafka().getBootstrapServers(),
                ProducerConfig.ACKS_CONFIG, properties.getKafka().getAcks(),
                ProducerConfig.BATCH_SIZE_CONFIG, properties.getKafka().getBatchSize(),
                ProducerConfig.LINGER_MS_CONFIG, properties.getKafka().getLingerMs(),
                ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG, properties.getKafka().getRequestTimeoutMs(),
                ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class,
                ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class
        );
        return new KafkaTemplate<>(new DefaultKafkaProducerFactory<>(configs));
    }

    /**
     * 状态管理服务
     */
    @Bean
    @ConditionalOnMissingBean
    public StateManagerService stateManagerService(
            StateDao stateDao,
            @Autowired(required = false) KafkaTemplate<String, Object> stateManagerKafkaTemplate,
            @Autowired(required = false) BusinessOpLogService businessOpLogService) {
        return new StateManagerServiceImpl(stateDao, stateManagerKafkaTemplate, stateManagerKafkaTemplate, businessOpLogService);
    }

    /**
     * JSON-RPC 自动导出器
     */
    @Bean
    @ConditionalOnProperty(prefix = "pay.state.manager.json-rpc", name = "enabled", havingValue = "true", matchIfMissing = true)
    @ConditionalOnClass(name = "com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImplExporter")
    @ConditionalOnMissingBean
    public AutoJsonRpcServiceImplExporter autoJsonRpcServiceImplExporter() {
        return new AutoJsonRpcServiceImplExporter();
    }

    /**
     * 状态管理 JSON-RPC 服务
     */
    @Bean
    @ConditionalOnProperty(prefix = "pay.state.manager.json-rpc", name = "enabled", havingValue = "true", matchIfMissing = true)
    @ConditionalOnMissingBean
    public StateManagerJsonRpcService stateManagerJsonRpcService() {
        return new StateManagerJsonRpcServiceImpl();
    }

    /**
     * JSON-RPC 服务导出器
     */
    @Bean
    @ConditionalOnProperty(prefix = "pay.state.manager.json-rpc", name = "enabled", havingValue = "true", matchIfMissing = true)
    @ConditionalOnClass(name = "com.googlecode.jsonrpc4j.spring.JsonServiceExporter")
    @ConditionalOnMissingBean(name = "stateManagerJsonRpcExporter")
    public JsonServiceExporter stateManagerJsonRpcExporter(StateManagerJsonRpcService service) {
        JsonServiceExporter exporter = new JsonServiceExporter();
        exporter.setService(service);
        exporter.setServiceInterface(StateManagerJsonRpcService.class);
        
        ObjectMapper objectMapper = new ObjectMapper();
        SimpleModule module = new SimpleModule();
        module.addDeserializer(AbstractEntity.class, new AbstractEntity.AbstractStateObjectDeserializer());
        objectMapper.registerModule(module);
        exporter.setObjectMapper(objectMapper);
        
        return exporter;
    }
}
