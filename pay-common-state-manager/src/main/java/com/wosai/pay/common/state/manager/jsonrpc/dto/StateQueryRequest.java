package com.wosai.pay.common.state.manager.jsonrpc.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.pay.common.state.manager.request.AbstractEntity;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 状态查询请求 DTO
 *
 * <AUTHOR>
 * @since 1.2.7-SNAPSHOT
 */
@Data
public class StateQueryRequest {

    /**
     * 业务类型
     * 例如: "settlement", "payment"
     */
    @JsonProperty("business")
    @NotBlank(message = "business不能为空")
    private String business;

    /**
     * 实体数据
     * 包含查询所需的实体信息，如 provider, providerMchId 等
     */
    @JsonProperty("entity")
    @NotNull(message = "entity不能为空")
    private AbstractEntity entity;


}
