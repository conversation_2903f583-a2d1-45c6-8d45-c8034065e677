package com.wosai.pay.common.state.manager;

import com.wosai.pay.common.state.manager.registry.StateManagerProcessorRegistry;
import com.wosai.pay.common.state.manager.request.OperationLogRequest;
import com.wosai.pay.common.state.manager.request.StateManagerRequest;
import com.wosai.pay.common.state.manager.service.StateManagerService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

import static org.junit.Assert.*;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@Slf4j
public class StateManagerTest {

    @Resource
    StateManagerService stateManagerService;

    private ProviderMchIdProcessor providerMchIdProcessor;

    @Before
    public void setUp() {
        // 创建并注册ProviderMchIdEntity的处理器
        providerMchIdProcessor = new ProviderMchIdProcessor();
        StateManagerProcessorRegistry.clear();
        StateManagerProcessorRegistry.register(providerMchIdProcessor);
    }

    @Test
    public void queryStateTest() {
        // 创建测试用的ProviderMchIdEntity实例
        ProviderMchIdEntity providerMchIdEntity = new ProviderMchIdEntity();
        providerMchIdEntity.setProvider(1001);
        providerMchIdEntity.setProviderMchId("test_provider_mch_id_001");
        providerMchIdEntity.setEntityType("ProviderMchIdEntity");

        // 创建状态管理请求
        StateManagerRequest<ProviderMchIdEntity> stateManagerRequest = new StateManagerRequest<>();
        stateManagerRequest.setBusiness("settlement");
        stateManagerRequest.setEntity(providerMchIdEntity);

        // 执行查询状态操作
        ProviderMchIdStateResult result = stateManagerService.queryState(stateManagerRequest);

        // 验证结果
        assertNotNull("查询结果不应该为null", result);
        assertNotNull("状态值不应该为null", result.getState());
        assertNotNull("子状态列表不应该为null", result.getSubStateList());
        assertEquals("Provider应该匹配", providerMchIdEntity.getProvider(), result.getProvider());
        assertEquals("ProviderMchId应该匹配", providerMchIdEntity.getProviderMchId(), result.getProviderMchId());

        // 验证处理器被正确调用
        assertTrue("处理器应该被注册",
                StateManagerProcessorRegistry.getSupportedEntityTypes().contains("ProviderMchIdEntity"));
    }


    @Test
    public void changeStateTest() {
        // 创建测试用的ProviderMchIdEntity实例
        ProviderMchIdEntity providerMchIdEntity = new ProviderMchIdEntity();
        providerMchIdEntity.setProvider(1001);
        providerMchIdEntity.setProviderMchId("test_provider_mch_id_001");
        providerMchIdEntity.setEntityType("ProviderMchIdEntity");

        // 创建状态管理请求
        StateManagerRequest<ProviderMchIdEntity> stateManagerRequest = new StateManagerRequest<>();
        stateManagerRequest.setBusiness("settlement");
        stateManagerRequest.setEntity(providerMchIdEntity);

        stateManagerRequest.setSubStateType(1);
        stateManagerRequest.setEnabled(true);


        OperationLogRequest  operationLogRequest = new OperationLogRequest();
        operationLogRequest.setOperatorUserId("123123");
        operationLogRequest.setOperatorUserName("zhangsan");
        operationLogRequest.setSceneTemplateCode("PI6V17G4KLPT");
        Boolean b = stateManagerService.changeState(stateManagerRequest, operationLogRequest);

    }

}
